// 导入日志系统模块
mod rizhixitong;
// 导入初始化模块
mod chushihua;
// 导入服务器模块
mod fuwuqi;
use chushihua::houduan_chushihua::houduan_chushihua;
use chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use serde::{Deserialize, Serialize};
use sqlx::Row;

// 异步主函数入口点
#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 默认启动后端服务器
    houduan_chushihua::qidong_fuwuqi().await?;
    Ok(())
}
